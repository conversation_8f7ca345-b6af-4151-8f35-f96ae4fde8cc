### Run

```bash
mkdir -p /data && mkdir -p /data/agent && cd /data/agent && curl -# -O https://mirror.lilh.net/priv/Gong/atlassian-agent.jar && java -jar atlassian-agent.jar
```

### Update

```bash
cd /data/agent && rm -rf atlassian-agent.jar && curl -# -O https://mirror.lilh.net/priv/Gong/atlassian-agent.jar && ls && java -jar atlassian-agent.jar
```

### Build

```bash
mvn clean compile && mvn clean package && cd target && rm -rf atlassian-agent.jar && mv ************************-dependencies.jar atlassian-agent.jar
```

### Usege

```bash
nano setenv.sh
```

新增一行加入

```bash
export CATALINA_OPTS="-javaagent:/data/agent/atlassian-agent.jar ${CATALINA_OPTS}"
```

### Generate License

#### Atlassian

```bash
java -jar /data/agent/atlassian-agent.jar -d -p <产品名称> -m <许可证邮箱> -n <许可证名称> -o <组织名称> -s <Server ID>
```

#### Plugin

```bash
java -jar /data/agent/atlassian-agent.jar -d -p <插件 App Key> -m <许可证邮箱> -n <许可证名称> -o <组织名称> -s <Server ID>
```

### 参考资料

<https://github.com/haxqer/jira>